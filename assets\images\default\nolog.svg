<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 1024 1024" height="1024px" width="1024px">
    <title>暂无订单 2</title>
    <defs>
        <radialGradient id="radialGradient-1" gradientTransform="translate(0.500000,0.024678),scale(0.663291,1.000000),rotate(91.510151),translate(-0.500000,-0.024678)" r="70.7270259%" fy="2.46780057%" fx="50.0000026%" cy="2.46780057%" cx="50.0000026%">
            <stop offset="0%" stop-opacity="0.15156181" stop-color="#0052D9"></stop>
            <stop offset="100%" stop-opacity="0" stop-color="#0052D9"></stop>
        </radialGradient>
        <linearGradient id="linearGradient-2" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="0%" stop-color="#72A0E8"></stop>
            <stop offset="99.39955%" stop-color="#3981F7"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-3" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="1.27618965%" stop-color="#B7D2FF"></stop>
            <stop offset="100%" stop-color="#93BBFF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-4" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="0%" stop-color="#72A0E8"></stop>
            <stop offset="99.39955%" stop-color="#3981F7"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-5" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="1.27618965%" stop-color="#B7D2FF"></stop>
            <stop offset="100%" stop-color="#93BBFF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-6" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="0%" stop-color="#72A0E8"></stop>
            <stop offset="99.39955%" stop-color="#3981F7"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-7" y2="100%" x2="50%" y1="0%" x1="50%">
            <stop offset="1.27618965%" stop-color="#B7D2FF"></stop>
            <stop offset="100%" stop-color="#93BBFF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-8" y2="0%" x2="0%" y1="100%" x1="0%">
            <stop offset="0%" stop-color="#7492FF"></stop>
            <stop offset="100%" stop-color="#496AE0"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-9" y2="0%" x2="0%" y1="100%" x1="0%">
            <stop offset="0%" stop-color="#9EBDEF"></stop>
            <stop offset="97.9669744%" stop-color="#71A2F2"></stop>
        </linearGradient>
        <path id="path-10" d="M33.3529412,0 C11.9094118,0 1,17.5518 1,39.2 L1,49 L56,49 L56,45.7333333 C56,2.84853333 33.3529412,0 33.3529412,0 Z"></path>
        <linearGradient id="linearGradient-11" y2="-9.3007569%" x2="18.8884733%" y1="61.340225%" x1="80.2370054%">
            <stop offset="0%" stop-color="#5E9BFF"></stop>
            <stop offset="100%" stop-color="#E2EDFF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-12" y2="42.223409%" x2="116.862811%" y1="52.1193772%" x1="22.7854167%">
            <stop offset="0%" stop-color="#5E9BFF"></stop>
            <stop offset="100%" stop-color="#E2EDFF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-13" y2="0%" x2="100%" y1="0%" x1="0%">
            <stop offset="0%" stop-color="#C6D2FF"></stop>
            <stop offset="100%" stop-color="#F1F4FF"></stop>
        </linearGradient>
        <linearGradient id="linearGradient-14" y2="0%" x2="0%" y1="100%" x1="0%">
            <stop offset="0%" stop-color="#DFE6FF"></stop>
            <stop offset="100%" stop-color="#FAFBFF"></stop>
        </linearGradient>
        <radialGradient id="radialGradient-15" r="66.169447%" fy="50%" fx="24.042307%" cy="50%" cx="24.042307%">
            <stop offset="0%" stop-color="#F2F7FF"></stop>
            <stop offset="100%" stop-color="#99C0FF"></stop>
        </radialGradient>
    </defs>
    <g fill-rule="evenodd" fill="none" stroke-width="1" stroke="none" id="暂无订单">
        <g transform="translate(104.000000, 273.000000)" id="编组">
            <g transform="translate(0.000000, 184.990000)">
                <polygon points="841.26 279 841.03 288.24 840.34 297.42 839.21 306.54 837.63 315.62 835.6 324.67 833.11 333.68 829.59 344.26 825.46 354.75 820.71 365.15 815.31 375.49 809.24 385.77 802.77 395.6 795.69 405.31 787.98 414.91 779.62 424.4 770.59 433.79 761.34 442.63 751.48 451.29 741 459.79 729.87 468.12 718.06 476.28 706.13 483.89 693.77 491.17 680.97 498.14 667.72 504.8 654 511.12 640.22 516.96 626.1 522.45 611.63 527.58 596.8 532.35 581.6 536.76 566.4 540.71 550.96 544.27 535.26 547.44 519.3 550.21 503.07 552.59 486.89 554.52 470.55 556.03 454.07 557.12 437.43 557.77 420.63 558 403.83 557.77 387.19 557.12 370.71 556.03 354.37 554.52 338.19 552.59 321.96 550.21 306 547.44 290.3 544.27 274.86 540.71 259.66 536.76 244.46 532.35 229.63 527.58 215.16 522.45 201.04 516.96 187.26 511.12 173.54 504.8 160.29 498.14 147.49 491.17 135.13 483.89 123.2 476.28 111.39 468.12 100.26 459.79 89.78 451.29 79.92 442.63 70.67 433.79 61.64 424.4 53.28 414.91 45.57 405.31 38.49 395.6 32.02 385.77 25.95 375.49 20.55 365.15 15.8 354.75 11.67 344.26 8.15 333.68 5.66 324.67 3.63 315.62 2.05 306.54 0.92 297.42 0.23 288.24 0 279 0.23 269.76 0.92 260.58 2.05 251.46 3.63 242.38 5.66 233.33 8.15 224.32 11.67 213.74 15.8 203.25 20.55 192.85 25.95 182.51 32.02 172.23 38.49 162.4 45.57 152.69 53.28 143.09 61.64 133.6 70.67 124.21 79.92 115.37 89.78 106.71 100.26 98.21 111.39 89.88 123.2 81.72 135.13 74.11 147.49 66.83 160.29 59.86 173.54 53.2 187.26 46.88 201.04 41.04 215.16 35.55 229.63 30.42 244.46 25.65 259.66 21.24 274.86 17.29 290.3 13.73 306 10.56 321.96 7.79 338.19 5.41 354.37 3.48 370.71 1.97 387.19 0.88 403.83 0.23 420.63 0 437.43 0.23 454.07 0.88 470.55 1.97 486.89 3.48 503.07 5.41 519.3 7.79 535.26 10.56 550.96 13.73 566.4 17.29 581.6 21.24 596.8 25.65 611.63 30.42 626.1 35.55 640.22 41.04 654 46.88 667.72 53.2 680.97 59.86 693.77 66.83 706.13 74.11 718.06 81.72 729.87 89.88 741 98.21 751.48 106.71 761.34 115.37 770.59 124.21 779.62 133.6 787.98 143.09 795.69 152.69 802.77 162.4 809.24 172.23 815.31 182.51 820.71 192.85 825.46 203.25 829.59 213.74 833.11 224.32 835.6 233.33 837.63 242.38 839.21 251.46 840.34 260.58 841.03 269.76" fill-rule="nonzero" fill="url(#radialGradient-1)" id="椭圆形"></polygon>
                <g id="路径" transform="translate(117.630000, 67.000000)">
                    <polygon points="20.77 30 31.15 30 31.15 84.67 30.95 86.12 30.45 87.36 29.63 88.44 28.58 89.28 27.37 89.8 25.96 90 25.96 90 24.55 89.8 23.35 89.28 22.29 88.44 21.48 87.36 20.97 86.12 20.77 84.67" fill-rule="nonzero" fill="#3F78D6"></polygon>
                    <polygon points="20.77 0 45 63.33 20.77 80" fill="url(#linearGradient-2)"></polygon>
                    <polygon points="20.77 0 0 70 20.77 80" fill="url(#linearGradient-3)"></polygon>
                </g>
                <g id="路径" transform="translate(699.630000, 32.000000)">
                    <polygon points="16 23 24 23 24 65.82 23.68 67.46 22.83 68.78 21.56 69.67 20 70 20 70 18.44 69.67 17.17 68.78 16.32 67.46 16 65.82" fill-rule="nonzero" fill="#3F78D6"></polygon>
                    <polygon points="16 0 35 49.08 16 62" fill="url(#linearGradient-4)"></polygon>
                    <polygon points="16 0 0 54.25 16 62" fill="url(#linearGradient-5)"></polygon>
                </g>
                <g id="路径" transform="translate(173.630000, 0.000000)">
                    <polygon points="16 23 24 23 24 64 23.68 65.57 22.83 66.83 21.57 67.68 20 68 20 68 18.43 67.68 17.17 66.83 16.32 65.57 16 64" fill-rule="nonzero" fill="#3F78D6"></polygon>
                    <polygon points="16 0 34 47.5 16 60" fill="url(#linearGradient-6)"></polygon>
                    <polygon points="16 0 0 52.5 16 60" fill="url(#linearGradient-7)"></polygon>
                </g>
            </g>
            <g transform="translate(286.000000, 0.000000)" id="编组-6">
                <g id="编组">
                    <path fill="#5577F0" id="路径" d="M33.3529412,0 C11.9094118,0 1,17.5518 1,39.2 L1,49 L56,49 L56,45.7333333 C56,2.84853333 33.3529412,0 33.3529412,0 Z"></path>
                    <g fill-rule="nonzero" id="路径">
                        <use xlink:href="#path-10" fill="url(#linearGradient-8)"></use>
                        <use xlink:href="#path-10" fill="url(#linearGradient-9)"></use>
                    </g>
                    <path fill="#FFE2D7" id="路径" d="M243,262 L243,38.9801249 C243,17.4524356 225.600637,0 204.137332,0 L20,0 C41.4633058,0 45.9407504,17.4524356 45.9407504,38.9801249 L45.9407504,236.125373 C45.9407504,236.125373 49.2099957,262 79.8995509,262 C134.559264,262 243,262 243,262 Z"></path>
                    <path fill="url(#linearGradient-11)" id="路径" d="M243,262 L243,38.9801249 C243,17.4524356 225.600637,0 204.137332,0 L20,0 C41.4633058,0 45.9407504,17.4524356 45.9407504,38.9801249 L45.9407504,236.125373 C45.9407504,236.125373 49.2099957,262 79.8995509,262 C134.559264,262 243,262 243,262 Z"></path>
                    <path fill="#5577F0" id="路径" d="M93.8814755,220 L275,220 C275,220 275,262 255.594444,262 C245.891666,262 42.1333257,262 74.4759193,262 C93.8814755,262 93.8814755,220 93.8814755,220 Z"></path>
                    <path fill="url(#linearGradient-12)" id="路径" d="M93.8814755,220 L275,220 C275,220 275,262 255.594444,262 C245.891666,262 42.1333257,262 74.4759193,262 C93.8814755,262 93.8814755,220 93.8814755,220 Z"></path>
                    <path fill="#FFFFFF" id="路径" d="M0.149543778,48.5185185 L3.38576641,48.5185185 C3.38576641,48.5185185 1.90357644,3.2345679 26.0393248,3.2345679 C42.2883986,3.2345679 45.4566606,33.3063457 45.4566606,42.0493827 C45.4566606,50.7924198 45.4566606,229.201481 45.4566606,236.123457 C45.4566606,243.045432 52.265673,262 71.3464416,262 C90.4272102,262 94,236.275481 94,219.950617 L90.7637774,216.716049 C90.7637774,216.716049 90.2912889,258.765432 71.3464416,258.765432 C52.4015943,258.765432 48.6928832,239.510049 48.6928832,229.654321 C48.6928832,219.798593 48.6928832,50.326642 48.6928832,42.0493827 C48.6928832,33.7721235 47.7543786,0 26.0393248,0 C-3.5656398,0 0.149543778,48.5185185 0.149543778,48.5185185 Z"></path>
                    <polygon points="91 217 271.77193 217 275 220 94.2280702 220" fill="#FFFFFF" id="路径"></polygon>
                    <polygon points="91 217 271.77193 217 275 220 94.2280702 220" fill-rule="nonzero" fill="url(#linearGradient-13)" id="路径"></polygon>
                </g>
                <path fill="#F9FAFF" id="形状" d="M222.136364,63.8604651 L119.181818,63.8604651 C115.389659,63.8604651 112.318182,60.759186 112.318182,56.9302326 C112.318182,53.1012791 115.389659,50 119.181818,50 L222.136364,50 C225.928523,50 229,53.1012791 229,56.9302326 C229,60.759186 225.928523,63.8604651 222.136364,63.8604651 Z M119.181818,140.093023 L146.636364,140.093023 C150.425091,140.093023 153.5,143.194302 153.5,147.023256 C153.5,150.852209 150.425091,153.953488 146.636364,153.953488 L119.181818,153.953488 C115.389659,153.953488 112.318182,150.852209 112.318182,147.023256 C112.318182,143.194302 115.389659,140.093023 119.181818,140.093023 Z M95.1590909,199 L84.8636364,199 C81.0714773,199 78,195.898721 78,192.069767 C78,188.240814 81.0714773,185.139535 84.8636364,185.139535 L95.1590909,185.139535 C98.9478182,185.139535 102.022727,188.240814 102.022727,192.069767 C102.022727,195.898721 98.9478182,199 95.1590909,199 Z M95.1590909,153.953488 L84.8636364,153.953488 C81.0714773,153.953488 78,150.852209 78,147.023256 C78,143.194302 81.0714773,140.093023 84.8636364,140.093023 L95.1590909,140.093023 C98.9478182,140.093023 102.022727,143.194302 102.022727,147.023256 C102.022727,150.852209 98.9478182,153.953488 95.1590909,153.953488 Z M95.1590909,108.906977 L84.8636364,108.906977 C81.0714773,108.906977 78,105.805698 78,101.976744 C78,98.1477907 81.0714773,95.0465116 84.8636364,95.0465116 L95.1590909,95.0465116 C98.9478182,95.0465116 102.022727,98.1477907 102.022727,101.976744 C102.022727,105.805698 98.9478182,108.906977 95.1590909,108.906977 Z M95.1590909,63.8604651 L84.8636364,63.8604651 C81.0714773,63.8604651 78,60.759186 78,56.9302326 C78,53.1012791 81.0714773,50 84.8636364,50 L95.1590909,50 C98.9478182,50 102.022727,53.1012791 102.022727,56.9302326 C102.022727,60.759186 98.9478182,63.8604651 95.1590909,63.8604651 Z M119.181818,185.139535 L146.636364,185.139535 C150.428523,185.139535 153.5,188.240814 153.5,192.069767 C153.5,195.898721 150.428523,199 146.636364,199 L119.181818,199 C115.389659,199 112.318182,195.898721 112.318182,192.069767 C112.318182,188.240814 115.389659,185.139535 119.181818,185.139535 Z M112.318182,101.976744 C112.318182,98.1477907 115.389659,95.0465116 119.181818,95.0465116 L218.704545,95.0465116 C222.496705,95.0465116 225.568182,98.1477907 225.568182,101.976744 C225.568182,105.805698 222.496705,108.906977 218.704545,108.906977 L119.181818,108.906977 C115.389659,108.906977 112.318182,105.805698 112.318182,101.976744 Z"></path>
                <path fill-rule="nonzero" fill="url(#linearGradient-14)" id="形状" d="M222.136364,63.8604651 L119.181818,63.8604651 C115.389659,63.8604651 112.318182,60.759186 112.318182,56.9302326 C112.318182,53.1012791 115.389659,50 119.181818,50 L222.136364,50 C225.928523,50 229,53.1012791 229,56.9302326 C229,60.759186 225.928523,63.8604651 222.136364,63.8604651 Z M119.181818,140.093023 L146.636364,140.093023 C150.425091,140.093023 153.5,143.194302 153.5,147.023256 C153.5,150.852209 150.425091,153.953488 146.636364,153.953488 L119.181818,153.953488 C115.389659,153.953488 112.318182,150.852209 112.318182,147.023256 C112.318182,143.194302 115.389659,140.093023 119.181818,140.093023 Z M95.1590909,199 L84.8636364,199 C81.0714773,199 78,195.898721 78,192.069767 C78,188.240814 81.0714773,185.139535 84.8636364,185.139535 L95.1590909,185.139535 C98.9478182,185.139535 102.022727,188.240814 102.022727,192.069767 C102.022727,195.898721 98.9478182,199 95.1590909,199 Z M95.1590909,153.953488 L84.8636364,153.953488 C81.0714773,153.953488 78,150.852209 78,147.023256 C78,143.194302 81.0714773,140.093023 84.8636364,140.093023 L95.1590909,140.093023 C98.9478182,140.093023 102.022727,143.194302 102.022727,147.023256 C102.022727,150.852209 98.9478182,153.953488 95.1590909,153.953488 Z M95.1590909,108.906977 L84.8636364,108.906977 C81.0714773,108.906977 78,105.805698 78,101.976744 C78,98.1477907 81.0714773,95.0465116 84.8636364,95.0465116 L95.1590909,95.0465116 C98.9478182,95.0465116 102.022727,98.1477907 102.022727,101.976744 C102.022727,105.805698 98.9478182,108.906977 95.1590909,108.906977 Z M95.1590909,63.8604651 L84.8636364,63.8604651 C81.0714773,63.8604651 78,60.759186 78,56.9302326 C78,53.1012791 81.0714773,50 84.8636364,50 L95.1590909,50 C98.9478182,50 102.022727,53.1012791 102.022727,56.9302326 C102.022727,60.759186 98.9478182,63.8604651 95.1590909,63.8604651 Z M119.181818,185.139535 L146.636364,185.139535 C150.428523,185.139535 153.5,188.240814 153.5,192.069767 C153.5,195.898721 150.428523,199 146.636364,199 L119.181818,199 C115.389659,199 112.318182,195.898721 112.318182,192.069767 C112.318182,188.240814 115.389659,185.139535 119.181818,185.139535 Z M112.318182,101.976744 C112.318182,98.1477907 115.389659,95.0465116 119.181818,95.0465116 L218.704545,95.0465116 C222.496705,95.0465116 225.568182,98.1477907 225.568182,101.976744 C225.568182,105.805698 222.496705,108.906977 218.704545,108.906977 L119.181818,108.906977 C115.389659,108.906977 112.318182,105.805698 112.318182,101.976744 Z"></path>
            </g>
            <polygon points="631.63 62.49 631.29 67.37 630.36 71.96 628.84 76.31 626.77 80.43 624.24 84.18 621.23 87.59 617.82 90.6 614.07 93.13 609.95 95.2 605.6 96.72 601.01 97.65 596.13 97.99 591.25 97.65 586.66 96.72 582.31 95.2 578.19 93.13 574.44 90.6 571.03 87.59 568.02 84.18 565.49 80.43 563.42 76.31 561.9 71.96 560.97 67.37 560.63 62.49 560.97 57.61 561.9 53.02 563.42 48.67 565.49 44.55 568.02 40.8 571.03 37.39 574.44 34.38 578.19 31.85 582.31 29.78 586.66 28.26 591.25 27.33 596.13 26.99 601.01 27.33 605.6 28.26 609.95 29.78 614.07 31.85 617.82 34.38 621.23 37.39 624.24 40.8 626.77 44.55 628.84 48.67 630.36 53.02 631.29 57.61" fill-rule="nonzero" fill="url(#radialGradient-15)" id="椭圆形"></polygon>
            <polygon points="305.63 150.49 305.3 154.05 304.41 157.31 302.97 160.33 301.03 163.07 298.71 165.39 295.97 167.33 292.95 168.77 289.69 169.66 286.13 169.99 282.57 169.66 279.31 168.77 276.29 167.33 273.55 165.39 271.23 163.07 269.29 160.33 267.85 157.31 266.96 154.05 266.63 150.49 266.96 146.93 267.85 143.67 269.29 140.65 271.23 137.91 273.55 135.59 276.29 133.65 279.31 132.21 282.57 131.32 286.13 130.99 289.69 131.32 292.95 132.21 295.97 133.65 298.71 135.59 301.03 137.91 302.97 140.65 304.41 143.67 305.3 146.93" fill-rule="nonzero" fill="url(#radialGradient-15)" fill-opacity="0.56253344" id="椭圆形备份"></polygon>
        </g>
    </g>
</svg>